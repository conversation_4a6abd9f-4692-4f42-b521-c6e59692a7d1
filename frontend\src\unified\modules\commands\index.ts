/**
 * 统一命令模块索引文件
 * 导出所有命令类和工厂函数，方便在新架构中使用
 * 适配新架构的统一类型系统
 */

// 导出统一命令工厂（推荐使用）
export { UnifiedCommandFactory } from './UnifiedCommandFactory'

// 导出所有时间轴命令类
export {
  UnifiedAddTimelineItemCommand,
  UnifiedRemoveTimelineItemCommand,
  UnifiedMoveTimelineItemCommand,
  UnifiedDuplicateTimelineItemCommand,
  UnifiedUpdateTransformCommand,
} from './unifiedTimelineCommands'

// 导出所有批量命令类
export {
  UnifiedBaseBatchCommand,
  UnifiedGenericBatchCommand,
  UnifiedBatchDeleteCommand,
  UnifiedBatchAutoArrangeTrackCommand,
  UnifiedBatchUpdatePropertiesCommand,
} from './unifiedBatchCommands'

// 导出所有关键帧命令类
export {
  UnifiedCreateKeyframeCommand,
  UnifiedDeleteKeyframeCommand,
  UnifiedUpdatePropertyCommand,
  UnifiedClearAllKeyframesCommand,
  UnifiedToggleKeyframeCommand,
} from './unifiedKeyframeCommands'

// 导出所有文本命令类
export {
  UnifiedAddTextItemCommand,
  UnifiedUpdateTextCommand,
  UnifiedRemoveTextItemCommand,
  UnifiedTextCommandFactory,
} from './unifiedTextCommands'

/**
 * 使用示例：
 *
 * // 推荐使用工厂函数创建命令
 * import { UnifiedCommandFactory } from '@/unified/modules/commands'
 *
 * const addCommand = UnifiedCommandFactory.createAddTimelineItemCommand(
 *   timelineItem,
 *   timelineModule,
 *   mediaModule,
 *   webavModule
 * )
 *
 * // 或者直接导入命令类
 * import { UnifiedAddTimelineItemCommand } from '@/unified/modules/commands'
 *
 * const addCommand = new UnifiedAddTimelineItemCommand(
 *   timelineItem,
 *   timelineModule,
 *   mediaModule,
 *   webavModule
 * )
 *
 * // 执行命令
 * await addCommand.execute()
 *
 * // 撤销命令
 * await addCommand.undo()
 */
